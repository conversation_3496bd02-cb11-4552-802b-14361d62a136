// متغيرات عامة
let selectedTable = null;
let selectionMode = false;

// متغيرات الكشط التلقائي الجديدة
let autoScrapeInProgress = false;
let autoScrapePaused = false;
let autoScrapeData = [];
let currentPaginationInfo = null;

// إضافة CSS لتنسيق تحديد الجداول
function injectTableSelectorStyles() {
  const link = document.createElement('link');
  link.rel = 'stylesheet';
  link.href = chrome.runtime.getURL('table-selector.css');
  document.head.appendChild(link);
}

// دالة ذكية للعثور على جميع الجداول في الصفحة
function findAllTables() {
  const tables = [];

  // البحث عن الجداول التقليدية
  const traditionalTables = document.querySelectorAll('table');
  traditionalTables.forEach(table => {
    if (isValidTable(table)) {
      tables.push(table);
    }
  });

  // البحث عن الجداول المبنية بـ div مع data-label
  const divTables = findDivBasedTables();
  tables.push(...divTables);

  return tables;
}

// دالة للتحقق من صحة الجدول
function isValidTable(table) {
  // التحقق من وجود صفوف وخلايا
  const rows = table.querySelectorAll('tr');
  if (rows.length === 0) return false;

  // التحقق من وجود خلايا في الصفوف
  const cells = table.querySelectorAll('td, th');
  if (cells.length === 0) return false;

  // التحقق من أن الجدول مرئي
  const style = window.getComputedStyle(table);
  if (style.display === 'none' || style.visibility === 'hidden') return false;

  return true;
}

// دالة للعثور على الجداول المبنية بـ div مع data-label
function findDivBasedTables() {
  const tables = [];

  // البحث عن العناصر التي تحتوي على data-label
  const elementsWithDataLabel = document.querySelectorAll('[data-label]');

  if (elementsWithDataLabel.length > 0) {
    // تجميع العناصر حسب الحاوي الأب
    const containers = new Set();

    elementsWithDataLabel.forEach(element => {
      // البحث عن الحاوي الأب الذي يحتوي على عدة عناصر بـ data-label
      let parent = element.parentElement;
      while (parent && parent !== document.body) {
        const childrenWithDataLabel = parent.querySelectorAll('[data-label]');
        if (childrenWithDataLabel.length >= 3) { // على الأقل 3 عناصر لتكوين جدول
          containers.add(parent);
          break;
        }
        parent = parent.parentElement;
      }
    });

    // تحويل الحاويات إلى جداول افتراضية
    containers.forEach(container => {
      if (isValidDivTable(container)) {
        // إضافة خاصية للتمييز بين الجداول التقليدية والـ div
        container.setAttribute('data-table-type', 'div-based');
        tables.push(container);
      }
    });
  }

  return tables;
}

// دالة للتحقق من صحة جدول الـ div
function isValidDivTable(container) {
  const elementsWithDataLabel = container.querySelectorAll('[data-label]');

  // التحقق من وجود عدد كافٍ من العناصر
  if (elementsWithDataLabel.length < 3) return false;

  // التحقق من أن الحاوي مرئي
  const style = window.getComputedStyle(container);
  if (style.display === 'none' || style.visibility === 'hidden') return false;

  return true;
}

// دالة للعثور على أفضل جدول في الصفحة
function findBestTable(tableType = 'auto') {
  let allTables = [];

  // تصفية الجداول حسب النوع المطلوب
  if (tableType === 'traditional') {
    // البحث عن الجداول التقليدية فقط
    const traditionalTables = document.querySelectorAll('table');
    traditionalTables.forEach(table => {
      if (isValidTable(table)) {
        allTables.push(table);
      }
    });
  } else if (tableType === 'responsive') {
    // البحث عن الجداول المتجاوبة فقط
    allTables = findDivBasedTables();
  } else {
    // البحث عن جميع أنواع الجداول
    allTables = findAllTables();
  }

  if (allTables.length === 0) return null;
  if (allTables.length === 1) return allTables[0];

  // ترتيب الجداول حسب الأولوية
  const scoredTables = allTables.map(table => ({
    table: table,
    score: calculateTableScore(table)
  }));

  // ترتيب تنازلي حسب النقاط
  scoredTables.sort((a, b) => b.score - a.score);

  return scoredTables[0].table;
}

// دالة لحساب نقاط الجدول لتحديد الأولوية
function calculateTableScore(table) {
  let score = 0;

  // نقاط إضافية للجداول التقليدية
  if (table.tagName === 'TABLE') {
    score += 10;

    // نقاط إضافية للجداول مع classes معينة
    if (table.classList.contains('table')) score += 5;
    if (table.classList.contains('table-bordered')) score += 3;
    if (table.classList.contains('qtable-white')) score += 3;

    // حساب عدد الصفوف والأعمدة
    const rows = table.querySelectorAll('tr');
    const cells = table.querySelectorAll('td, th');

    score += Math.min(rows.length, 20); // حد أقصى 20 نقطة للصفوف
    score += Math.min(cells.length / 10, 10); // حد أقصى 10 نقاط للخلايا
  }

  // نقاط للجداول المبنية بـ div
  if (table.getAttribute('data-table-type') === 'div-based') {
    score += 8;

    const elementsWithDataLabel = table.querySelectorAll('[data-label]');
    score += Math.min(elementsWithDataLabel.length / 5, 15); // حد أقصى 15 نقطة
  }

  // نقاط إضافية للجداول المرئية والكبيرة
  const rect = table.getBoundingClientRect();
  if (rect.width > 300) score += 5;
  if (rect.height > 200) score += 5;

  // نقاط إضافية للجداول في منطقة المحتوى الرئيسي
  if (isInMainContent(table)) score += 10;

  return score;
}

// دالة للتحقق من وجود الجدول في المحتوى الرئيسي
function isInMainContent(table) {
  // البحث عن العناصر الرئيسية للمحتوى
  const mainSelectors = ['main', '.main', '#main', '.content', '#content', '.container', 'article'];

  let parent = table.parentElement;
  while (parent && parent !== document.body) {
    for (const selector of mainSelectors) {
      if (parent.matches && parent.matches(selector)) {
        return true;
      }
    }
    parent = parent.parentElement;
  }

  return false;
}

// دالة لتفعيل وضع تحديد الجدول
function enableTableSelectionMode() {
  selectionMode = true;

  // إضافة مؤشر للجداول عند تمرير الماوس عليها
  const tables = findAllTables();
  tables.forEach(table => {
    table.addEventListener('mouseover', handleTableMouseOver);
    table.addEventListener('mouseout', handleTableMouseOut);
    table.addEventListener('click', handleTableClick);
  });

  // إضافة تنسيق CSS
  injectTableSelectorStyles();
}

// دالة لإلغاء تفعيل وضع تحديد الجدول
function disableTableSelectionMode() {
  selectionMode = false;

  // إزالة مؤشر للجداول عند تمرير الماوس عليها
  const tables = findAllTables();
  tables.forEach(table => {
    table.removeEventListener('mouseover', handleTableMouseOver);
    table.removeEventListener('mouseout', handleTableMouseOut);
    table.removeEventListener('click', handleTableClick);
    table.classList.remove('table-highlight');
  });
}

// معالجة حدث تمرير الماوس فوق الجدول
function handleTableMouseOver(event) {
  if (selectionMode) {
    event.currentTarget.classList.add('table-highlight');
  }
}

// معالجة حدث إزالة الماوس من فوق الجدول
function handleTableMouseOut(event) {
  if (selectionMode) {
    event.currentTarget.classList.remove('table-highlight');
  }
}

// معالجة حدث النقر على الجدول
function handleTableClick(event) {
  if (selectionMode) {
    event.preventDefault();
    event.stopPropagation();

    // تحديد الجدول المحدد
    selectedTable = event.currentTarget;

    // إلغاء تفعيل وضع التحديد
    disableTableSelectionMode();

    // إضافة فئة للجدول المحدد
    selectedTable.classList.add('table-selected');

    // كشط الجدول تلقائيًا
    scrapeSelectedTable();
  }
}

// دالة لكشط الجدول المحدد
function scrapeSelectedTable() {
  try {
    if (!selectedTable) {
      throw new Error("لم يتم تحديد أي جدول");
    }

    // استخراج بيانات الجدول
    const data = scrapeTableData(selectedTable);

    // تحويل البيانات إلى تنسيق Excel
    const excelData = convertToExcel(data);

    // إرسال البيانات إلى background.js لتحميلها
    chrome.runtime.sendMessage({
      action: "downloadExcel",
      data: excelData,
      format: 'csv'
    });

    // إزالة فئة التحديد بعد فترة
    setTimeout(() => {
      if (selectedTable) {
        selectedTable.classList.remove('table-selected');
      }
    }, 3000);

    return true;
  } catch (error) {
    console.error("خطأ في كشط الجدول المحدد:", error);
    return false;
  }
}

// استماع للرسائل من popup.js
chrome.runtime.onMessage.addListener(function(request, _sender, sendResponse) {
  // تفعيل وضع تحديد الجدول
  if (request.action === "enableTableSelection") {
    try {
      enableTableSelectionMode();
      sendResponse({ success: true });
    } catch (error) {
      sendResponse({ success: false, error: error.message });
    }
    return true;
  }

  // بدء الكشط التلقائي الجديد
  else if (request.action === "startAutoScrape") {
    (async function() {
      try {
        console.log('🚀 بدء الكشط التلقائي...');

        // اكتشاف عناصر التنقل أولاً
        const paginationInfo = detectPaginationElements();

        if (!paginationInfo) {
          sendResponse({
            success: false,
            error: 'لم يتم العثور على عناصر التنقل في الصفحة'
          });
          return;
        }

        // إرسال معلومات أولية
        sendResponse({
          success: true,
          totalPages: paginationInfo.totalPages,
          currentPage: paginationInfo.currentPage,
          paginationType: paginationInfo.type
        });

        // بدء عملية الكشط التلقائي
        const result = await autoScrapeAllPages(request.options);

        // إرسال النتيجة النهائية عبر رسالة منفصلة
        chrome.runtime.sendMessage({
          action: "autoScrapeComplete",
          result: result
        });

      } catch (error) {
        console.error('خطأ في الكشط التلقائي:', error);
        sendResponse({
          success: false,
          error: error.message
        });
      }
    })();
    return true; // للحفاظ على قناة الاتصال مفتوحة
  }

  // الحصول على تقدم الكشط التلقائي
  else if (request.action === "getAutoScrapeProgress") {
    if (autoScrapeInProgress && currentPaginationInfo) {
      sendResponse({
        success: true,
        currentPage: currentPaginationInfo.currentPage,
        totalPages: currentPaginationInfo.totalPages,
        scrapedPages: autoScrapeData.length,
        completed: !autoScrapeInProgress
      });
    } else if (autoScrapeData.length > 0) {
      // العملية مكتملة
      sendResponse({
        success: true,
        currentPage: currentPaginationInfo?.totalPages || autoScrapeData.length,
        totalPages: currentPaginationInfo?.totalPages || autoScrapeData.length,
        scrapedPages: autoScrapeData.length,
        completed: true,
        data: autoScrapeData
      });
    } else {
      sendResponse({
        success: false,
        error: 'لا توجد عملية كشط تلقائي جارية'
      });
    }
    return true;
  }

  // إيقاف مؤقت للكشط التلقائي
  else if (request.action === "pauseAutoScrape") {
    autoScrapePaused = true;
    console.log('⏸️ تم إيقاف الكشط التلقائي مؤقتاً');
    sendResponse({ success: true });
    return true;
  }

  // استئناف الكشط التلقائي
  else if (request.action === "resumeAutoScrape") {
    autoScrapePaused = false;
    console.log('▶️ تم استئناف الكشط التلقائي');
    sendResponse({ success: true });
    return true;
  }

  // إيقاف الكشط التلقائي
  else if (request.action === "stopAutoScrape") {
    autoScrapeInProgress = false;
    autoScrapePaused = false;
    console.log('🛑 تم إيقاف الكشط التلقائي');
    sendResponse({ success: true });
    return true;
  }

  // فحص وجود عناصر التنقل
  else if (request.action === "checkPagination") {
    try {
      const paginationInfo = detectPaginationElements();

      if (paginationInfo && paginationInfo.totalPages > 1) {
        sendResponse({
          success: true,
          hasPagination: true,
          totalPages: paginationInfo.totalPages,
          currentPage: paginationInfo.currentPage,
          paginationType: paginationInfo.type
        });
      } else {
        sendResponse({
          success: true,
          hasPagination: false,
          totalPages: 1,
          currentPage: 1
        });
      }
    } catch (error) {
      sendResponse({
        success: false,
        error: error.message,
        hasPagination: false
      });
    }
    return true;
  }

  // كشط الجدول (الوظيفة الأصلية)
  else if (request.action === "scrapeTable") {
    try {
      const options = request.options || {};

      // تحديد الجدول المراد كشطه
      let table;

      if (options.selectionMode === 'manual' && selectedTable) {
        // استخدام الجدول المحدد يدويًا
        table = selectedTable;
      } else {
        // البحث عن الجدول تلقائيًا باستخدام الدالة المحسنة
        table = findBestTable(options.tableType);
      }

      if (!table) {
        sendResponse({ success: false, error: "لم يتم العثور على الجدول في الصفحة" });
        return true;
      }

      // استخراج بيانات الجدول
      const data = scrapeTableData(table);

      // التحقق من وضع الكشط (متعدد الصفحات أم لا)
      if (options.multiPage) {
        // إذا كان هناك بيانات موجودة من صفحات سابقة
        let allData = options.existingData || [];

        // إضافة بيانات الصفحة الحالية
        allData.push(data);

        // إرسال البيانات إلى popup.js
        sendResponse({ success: true, data: allData });
      } else {
        // وضع الصفحة الواحدة - تحويل البيانات إلى CSV وتحميلها مباشرة
        const csvData = convertToExcel(data);

        // إرسال البيانات إلى background.js لتحميلها
        chrome.runtime.sendMessage({
          action: "downloadExcel",
          data: csvData,
          format: 'csv'
        });

        sendResponse({ success: true });
      }
    } catch (error) {
      sendResponse({ success: false, error: error.message });
    }
    return true;
  }
});

// دالة لاستخراج بيانات الجدول
function scrapeTableData(table) {
  // التحقق من نوع الجدول
  if (table.getAttribute('data-table-type') === 'div-based') {
    return scrapeDivBasedTable(table);
  } else {
    return scrapeTraditionalTable(table);
  }
}

// دالة لاستخراج بيانات الجدول التقليدي
function scrapeTraditionalTable(table) {
  const data = [];

  // استخراج الرؤوس
  const headerRows = table.querySelectorAll('thead tr');
  const headers = [];

  // الصف الأول من الرؤوس
  const firstHeaderRow = headerRows[0];
  const firstHeaderCells = firstHeaderRow.querySelectorAll('th');

  firstHeaderCells.forEach(cell => {
    const colspan = parseInt(cell.getAttribute('colspan')) || 1;
    const rowspan = parseInt(cell.getAttribute('rowspan')) || 1;

    if (rowspan > 1) {
      // إذا كان الخلية تمتد على أكثر من صف، أضفها مباشرة إلى المصفوفة النهائية
      headers.push(cell.textContent.trim());
    } else {
      // إذا كان الخلية تمتد على أكثر من عمود، كرر النص
      for (let i = 0; i < colspan; i++) {
        headers.push(cell.textContent.trim());
      }
    }
  });

  // الصف الثاني من الرؤوس (إذا وجد)
  if (headerRows.length > 1) {
    const secondHeaderRow = headerRows[1];
    const secondHeaderCells = secondHeaderRow.querySelectorAll('th');

    let headerIndex = 0;
    secondHeaderCells.forEach(cell => {
      // تخطي الخلايا التي تم تغطيتها بواسطة rowspan من الصف الأول
      while (headerIndex < headers.length && headers[headerIndex].includes('الرقم التسلسلي')) {
        headerIndex++;
      }

      if (headerIndex < headers.length) {
        // استبدال النص الحالي بالنص الجديد أو إضافته كعنوان فرعي
        headers[headerIndex] = `${headers[headerIndex]} - ${cell.textContent.trim()}`;
        headerIndex++;
      } else {
        // إضافة عناوين جديدة إذا لزم الأمر
        headers.push(cell.textContent.trim());
      }
    });
  }

  // إضافة الرؤوس كصف أول في البيانات
  data.push(headers);

  // استخراج بيانات الصفوف
  const rows = table.querySelectorAll('tbody tr');

  rows.forEach(row => {
    const rowData = [];
    const cells = row.querySelectorAll('td');

    cells.forEach(cell => {
      // البحث عن النص داخل الخلية
      const span = cell.querySelector('span');
      if (span) {
        rowData.push(span.textContent.trim());
      } else {
        rowData.push(cell.textContent.trim());
      }
    });

    data.push(rowData);
  });

  // استخراج بيانات تذييل الجدول
  const footerRows = table.querySelectorAll('tfoot tr');

  footerRows.forEach(row => {
    const rowData = [];
    const cells = row.querySelectorAll('td');

    cells.forEach(cell => {
      // البحث عن النص داخل الخلية
      const span = cell.querySelector('span');
      if (span) {
        rowData.push(span.textContent.trim());
      } else {
        rowData.push(cell.textContent.trim());
      }
    });

    data.push(rowData);
  });

  return data;
}

// دالة لاستخراج بيانات الجدول المبني بـ div مع data-label
function scrapeDivBasedTable(container) {
  const data = [];
  const elementsWithDataLabel = container.querySelectorAll('[data-label]');

  if (elementsWithDataLabel.length === 0) return data;

  // استخراج الرؤوس من data-label attributes
  const headers = [];
  const headerSet = new Set();

  elementsWithDataLabel.forEach(element => {
    const label = element.getAttribute('data-label');
    if (label && !headerSet.has(label)) {
      headers.push(label);
      headerSet.add(label);
    }
  });

  // إضافة الرؤوس كصف أول
  data.push(headers);

  // تجميع العناصر في صفوف
  const rows = groupElementsIntoRows(elementsWithDataLabel, headers);

  // إضافة بيانات الصفوف
  rows.forEach(row => {
    const rowData = [];
    headers.forEach(header => {
      rowData.push(row[header] || '');
    });
    data.push(rowData);
  });

  return data;
}

// دالة لتجميع العناصر في صفوف
function groupElementsIntoRows(elements, headers) {
  const rows = [];

  // تجميع العناصر حسب الموقع أو الحاوي المباشر
  const elementsByContainer = new Map();

  elements.forEach(element => {
    const label = element.getAttribute('data-label');
    if (!label) return;

    // البحث عن الحاوي المباشر للعنصر (مثل tr أو div)
    let container = element.parentElement;

    // إذا كان العنصر نفسه هو td، استخدم tr كحاوي
    if (element.tagName === 'TD') {
      container = element.closest('tr');
    }

    if (!elementsByContainer.has(container)) {
      elementsByContainer.set(container, {});
    }

    const containerData = elementsByContainer.get(container);

    // استخراج النص من العنصر
    let text = '';
    const span = element.querySelector('span');
    if (span) {
      text = span.textContent.trim();
    } else {
      text = element.textContent.trim();
    }

    containerData[label] = text;
  });

  // تحويل البيانات إلى مصفوفة صفوف
  elementsByContainer.forEach(containerData => {
    // التحقق من أن الصف يحتوي على بيانات كافية
    const dataCount = Object.keys(containerData).length;
    if (dataCount >= Math.min(3, headers.length)) {
      rows.push(containerData);
    }
  });

  return rows;
}

// دالة لتحويل البيانات إلى تنسيق CSV (يمكن فتحه بواسطة Excel)
function convertToExcel(data) {
  let csv = '';

  // تحويل كل صف إلى سلسلة CSV
  data.forEach(row => {
    // تحويل كل خلية في الصف
    const csvRow = row.map(cell => {
      // إذا كانت الخلية تحتوي على فاصلة أو اقتباس أو سطر جديد، ضعها بين علامات اقتباس
      if (cell.includes(',') || cell.includes('"') || cell.includes('\n')) {
        // استبدال علامات الاقتباس بعلامتي اقتباس متتاليتين
        return '"' + cell.replace(/"/g, '""') + '"';
      }
      return cell;
    }).join(',');

    csv += csvRow + '\n';
  });

  return csv;
}

// ==================== دوال الكشط التلقائي الجديدة ====================

// دالة ذكية للتعرف على عناصر التنقل (pagination)
function detectPaginationElements() {
  console.log('🔍 بدء البحث عن عناصر التنقل في الصفحة...');

  // أنماط CSS selectors للبحث عن عناصر التنقل
  const paginationSelectors = [
    // النمط الأساسي من المثال المعطى
    '.dataTable-pagination a[data-page]',
    '.dataTable-pagination-list a[data-page]',
    'nav.dataTable-pagination a[data-page]',

    // أنماط شائعة أخرى
    '.pagination a',
    '.pagination li a',
    '.page-numbers a',
    '.pager a',
    '.paginate a',
    '.wp-pagenavi a',

    // أنماط بـ data attributes
    '[data-page]',
    '[data-dt-idx]',
    '[data-pagination]',

    // أنماط بـ href patterns
    'a[href*="page="]',
    'a[href*="p="]',
    'a[href*="pagenum="]',

    // أنماط بـ onclick patterns
    '[onclick*="page"]',
    '[onclick*="goToPage"]',
    '[onclick*="setPage"]'
  ];

  console.log(`🔍 سيتم البحث باستخدام ${paginationSelectors.length} نمط مختلف...`);

  // البحث عن عناصر التنقل
  for (let i = 0; i < paginationSelectors.length; i++) {
    const selector = paginationSelectors[i];
    try {
      console.log(`🔍 البحث باستخدام: ${selector}`);
      const elements = document.querySelectorAll(selector);

      if (elements.length > 0) {
        console.log(`✅ تم العثور على ${elements.length} عنصر تنقل باستخدام: ${selector}`);

        // طباعة تفاصيل العناصر المكتشفة
        elements.forEach((el, index) => {
          console.log(`   العنصر ${index + 1}:`, {
            tagName: el.tagName,
            textContent: el.textContent.trim(),
            dataPage: el.getAttribute('data-page'),
            href: el.getAttribute('href'),
            onclick: el.getAttribute('onclick'),
            className: el.className
          });
        });

        const paginationInfo = analyzePaginationElements(elements, selector);
        if (paginationInfo && paginationInfo.totalPages > 1) {
          console.log('📊 معلومات التنقل النهائية:', paginationInfo);
          return paginationInfo;
        } else {
          console.log('⚠️ تم العثور على عناصر ولكن لا توجد صفحات متعددة');
        }
      } else {
        console.log(`❌ لم يتم العثور على عناصر باستخدام: ${selector}`);
      }
    } catch (error) {
      console.warn(`⚠️ خطأ في البحث باستخدام selector: ${selector}`, error);
    }
  }

  // البحث بالنصوص إذا لم نجد بالـ selectors
  console.log('🔍 البحث بالنصوص كبديل...');
  const textResult = detectPaginationByText();
  if (textResult) {
    console.log('✅ تم العثور على تنقل بالنصوص:', textResult);
  } else {
    console.log('❌ لم يتم العثور على أي عناصر تنقل في الصفحة');
  }

  return textResult;
}

// دالة لتحليل عناصر التنقل المكتشفة
function analyzePaginationElements(elements, selector) {
  const paginationInfo = {
    elements: Array.from(elements),
    selector: selector,
    currentPage: 1,
    totalPages: 1,
    nextPageElement: null,
    previousPageElement: null,
    pageElements: [],
    type: 'unknown'
  };

  // تحليل عناصر التنقل حسب النوع
  if (selector.includes('data-page')) {
    return analyzeDataPageElements(elements, paginationInfo);
  } else if (selector.includes('pagination') || selector.includes('page-numbers')) {
    return analyzeStandardPaginationElements(elements, paginationInfo);
  } else if (selector.includes('href')) {
    return analyzeHrefPaginationElements(elements, paginationInfo);
  } else if (selector.includes('onclick')) {
    return analyzeOnclickPaginationElements(elements, paginationInfo);
  }

  return null;
}

// دالة لتحليل عناصر data-page (النمط الأساسي)
function analyzeDataPageElements(elements, paginationInfo) {
  console.log('🔍 تحليل عناصر data-page...');
  paginationInfo.type = 'data-page';

  const pageNumbers = [];

  elements.forEach((element, index) => {
    const pageAttr = element.getAttribute('data-page');
    console.log(`   العنصر ${index + 1}: data-page="${pageAttr}", نص="${element.textContent.trim()}"`);

    if (pageAttr) {
      const pageNum = parseInt(pageAttr);
      if (!isNaN(pageNum)) {
        pageNumbers.push(pageNum);

        // فحص الحالة النشطة بطرق متعددة
        const isActive = element.closest('li')?.classList.contains('active') ||
                        element.classList.contains('active') ||
                        element.classList.contains('current') ||
                        element.closest('li')?.classList.contains('current') ||
                        element.parentElement?.classList.contains('active');

        paginationInfo.pageElements.push({
          element: element,
          pageNumber: pageNum,
          isActive: isActive
        });

        console.log(`     الصفحة ${pageNum}: نشطة=${isActive}`);

        // تحديد الصفحة الحالية
        if (isActive) {
          paginationInfo.currentPage = pageNum;
          console.log(`     ✅ تم تحديد الصفحة الحالية: ${pageNum}`);
        }
      }
    }
  });

  console.log(`📊 تم العثور على ${pageNumbers.length} صفحة: [${pageNumbers.join(', ')}]`);

  if (pageNumbers.length > 0) {
    paginationInfo.totalPages = Math.max(...pageNumbers);

    // إذا لم يتم تحديد الصفحة الحالية، استخدم الصفحة الأولى
    if (paginationInfo.currentPage === 1 && pageNumbers.length > 1) {
      paginationInfo.currentPage = Math.min(...pageNumbers);
      console.log(`🔄 تم تعيين الصفحة الحالية افتراضياً إلى: ${paginationInfo.currentPage}`);
    }

    // البحث عن عناصر التنقل (التالي/السابق)
    const parentContainer = elements[0].closest('.dataTable-pagination, .pagination, .page-numbers, nav');
    console.log('🔍 البحث عن أزرار التنقل في الحاوي:', parentContainer?.className);

    if (parentContainer) {
      // البحث عن زر "التالي"
      const nextElement = Array.from(parentContainer.querySelectorAll('a, button')).find(a => {
        const text = a.textContent.trim();
        return text.includes('›') || text.includes('»') ||
               text.includes('التالي') || text.includes('Next') ||
               text === '>' || a.classList.contains('next');
      });

      if (nextElement) {
        paginationInfo.nextPageElement = nextElement;
        console.log('✅ تم العثور على زر التالي:', nextElement.textContent.trim());
      }

      // البحث عن زر "السابق"
      const prevElement = Array.from(parentContainer.querySelectorAll('a, button')).find(a => {
        const text = a.textContent.trim();
        return text.includes('‹') || text.includes('«') ||
               text.includes('السابق') || text.includes('Previous') ||
               text === '<' || a.classList.contains('prev');
      });

      if (prevElement) {
        paginationInfo.previousPageElement = prevElement;
        console.log('✅ تم العثور على زر السابق:', prevElement.textContent.trim());
      }
    }

    console.log(`📊 النتيجة النهائية: ${paginationInfo.totalPages} صفحة، الحالية: ${paginationInfo.currentPage}`);
    return paginationInfo;
  }

  console.log('❌ لم يتم العثور على صفحات صالحة');
  return null;
}

// دالة لتحليل عناصر التنقل القياسية
function analyzeStandardPaginationElements(elements, paginationInfo) {
  paginationInfo.type = 'standard';

  const pageNumbers = [];

  elements.forEach(element => {
    const text = element.textContent.trim();
    const pageNum = parseInt(text);

    if (!isNaN(pageNum)) {
      pageNumbers.push(pageNum);
      paginationInfo.pageElements.push({
        element: element,
        pageNumber: pageNum,
        isActive: element.classList.contains('current') ||
                 element.classList.contains('active') ||
                 element.closest('li')?.classList.contains('current') ||
                 element.closest('li')?.classList.contains('active')
      });

      if (element.classList.contains('current') ||
          element.classList.contains('active') ||
          element.closest('li')?.classList.contains('current') ||
          element.closest('li')?.classList.contains('active')) {
        paginationInfo.currentPage = pageNum;
      }
    } else {
      // التحقق من أزرار التنقل
      if (text.includes('التالي') || text.includes('Next') || text.includes('›') || text.includes('»')) {
        paginationInfo.nextPageElement = element;
      } else if (text.includes('السابق') || text.includes('Previous') || text.includes('‹') || text.includes('«')) {
        paginationInfo.previousPageElement = element;
      }
    }
  });

  if (pageNumbers.length > 0) {
    paginationInfo.totalPages = Math.max(...pageNumbers);
    return paginationInfo;
  }

  return null;
}

// دالة لتحليل عناصر href pagination
function analyzeHrefPaginationElements(elements, paginationInfo) {
  paginationInfo.type = 'href-based';

  const pageNumbers = [];

  elements.forEach(element => {
    const href = element.getAttribute('href');
    if (href) {
      // استخراج رقم الصفحة من href
      const pageMatch = href.match(/page=(\d+)|p=(\d+)|pagenum=(\d+)/i);
      if (pageMatch) {
        const pageNum = parseInt(pageMatch[1] || pageMatch[2] || pageMatch[3]);
        if (!isNaN(pageNum)) {
          pageNumbers.push(pageNum);
          paginationInfo.pageElements.push({
            element: element,
            pageNumber: pageNum,
            isActive: element.classList.contains('current') ||
                     element.classList.contains('active')
          });
        }
      }
    }
  });

  if (pageNumbers.length > 0) {
    paginationInfo.totalPages = Math.max(...pageNumbers);
    paginationInfo.currentPage = Math.min(...pageNumbers); // تقدير الصفحة الحالية
    return paginationInfo;
  }

  return null;
}

// دالة لتحليل عناصر onclick pagination
function analyzeOnclickPaginationElements(elements, paginationInfo) {
  paginationInfo.type = 'onclick-based';

  const pageNumbers = [];

  elements.forEach(element => {
    const onclick = element.getAttribute('onclick');
    if (onclick) {
      // استخراج رقم الصفحة من onclick
      const pageMatch = onclick.match(/page[^\d]*(\d+)|goToPage[^\d]*(\d+)|setPage[^\d]*(\d+)/i);
      if (pageMatch) {
        const pageNum = parseInt(pageMatch[1] || pageMatch[2] || pageMatch[3]);
        if (!isNaN(pageNum)) {
          pageNumbers.push(pageNum);
          paginationInfo.pageElements.push({
            element: element,
            pageNumber: pageNum,
            isActive: element.classList.contains('current') ||
                     element.classList.contains('active')
          });
        }
      }
    }
  });

  if (pageNumbers.length > 0) {
    paginationInfo.totalPages = Math.max(...pageNumbers);
    paginationInfo.currentPage = Math.min(...pageNumbers); // تقدير الصفحة الحالية
    return paginationInfo;
  }

  return null;
}

// دالة للبحث عن التنقل بالنصوص
function detectPaginationByText() {
  const textPatterns = [
    /صفحة\s*(\d+)\s*من\s*(\d+)/i,
    /page\s*(\d+)\s*of\s*(\d+)/i,
    /(\d+)\s*\/\s*(\d+)/,
    /الصفحة\s*(\d+)/i
  ];

  const allText = document.body.textContent;

  for (const pattern of textPatterns) {
    const match = allText.match(pattern);
    if (match) {
      const currentPage = parseInt(match[1]);
      const totalPages = match[2] ? parseInt(match[2]) : currentPage;

      if (!isNaN(currentPage) && !isNaN(totalPages) && totalPages > 1) {
        return {
          type: 'text-based',
          currentPage: currentPage,
          totalPages: totalPages,
          elements: [],
          pageElements: [],
          nextPageElement: findNextPageButton(),
          previousPageElement: findPreviousPageButton()
        };
      }
    }
  }

  return null;
}

// دالة للبحث عن زر الصفحة التالية
function findNextPageButton() {
  const nextSelectors = [
    '.next', '.next-page', '.page-next'
  ];

  // البحث بالـ selectors أولاً
  for (const selector of nextSelectors) {
    const element = document.querySelector(selector);
    if (element && !element.disabled && !element.classList.contains('disabled')) {
      return element;
    }
  }

  // البحث بالنصوص
  const nextTexts = ['التالي', 'Next', '›', '»'];
  for (const text of nextTexts) {
    const element = Array.from(document.querySelectorAll('a, button')).find(el =>
      el.textContent.trim() === text || el.textContent.includes(text)
    );
    if (element && !element.disabled && !element.classList.contains('disabled')) {
      return element;
    }
  }

  return null;
}

// دالة للبحث عن زر الصفحة السابقة
function findPreviousPageButton() {
  const prevSelectors = [
    '.prev', '.previous', '.prev-page', '.page-prev'
  ];

  // البحث بالـ selectors أولاً
  for (const selector of prevSelectors) {
    const element = document.querySelector(selector);
    if (element && !element.disabled && !element.classList.contains('disabled')) {
      return element;
    }
  }

  // البحث بالنصوص
  const prevTexts = ['السابق', 'Previous', '‹', '«'];
  for (const text of prevTexts) {
    const element = Array.from(document.querySelectorAll('a, button')).find(el =>
      el.textContent.trim() === text || el.textContent.includes(text)
    );
    if (element && !element.disabled && !element.classList.contains('disabled')) {
      return element;
    }
  }

  return null;
}

// دالة للتنقل إلى الصفحة التالية
async function navigateToNextPage(paginationInfo) {
  console.log('🔄 محاولة التنقل إلى الصفحة التالية...');
  console.log('📊 معلومات التنقل الحالية:', {
    currentPage: paginationInfo.currentPage,
    totalPages: paginationInfo.totalPages,
    hasNextButton: !!paginationInfo.nextPageElement,
    pageElementsCount: paginationInfo.pageElements?.length || 0
  });

  if (!paginationInfo) {
    throw new Error('معلومات التنقل غير متوفرة');
  }

  let success = false;
  const nextPageNumber = paginationInfo.currentPage + 1;

  // الطريقة 1: محاولة التنقل إلى الصفحة التالية مباشرة (الأولوية)
  if (paginationInfo.pageElements && nextPageNumber <= paginationInfo.totalPages) {
    const nextPageElement = paginationInfo.pageElements.find(pe => pe.pageNumber === nextPageNumber);

    if (nextPageElement && nextPageElement.element) {
      console.log(`📍 التنقل مباشرة إلى الصفحة ${nextPageNumber}`);
      success = await clickElement(nextPageElement.element);

      if (success) {
        console.log(`✅ نجح التنقل المباشر إلى الصفحة ${nextPageNumber}`);
        return true;
      }
    } else {
      console.log(`⚠️ لم يتم العثور على عنصر الصفحة ${nextPageNumber}`);
    }
  }

  // الطريقة 2: محاولة التنقل باستخدام زر "التالي"
  if (!success && paginationInfo.nextPageElement) {
    console.log('📍 استخدام زر التالي للتنقل');
    success = await clickElement(paginationInfo.nextPageElement);

    if (success) {
      console.log('✅ نجح التنقل باستخدام زر التالي');
      return true;
    }
  }

  // الطريقة 3: البحث عن رابط الصفحة التالية في الصفحة
  if (!success) {
    console.log('🔍 البحث عن رابط الصفحة التالية في الصفحة...');

    // البحث بـ data-page
    const directPageLink = document.querySelector(`[data-page="${nextPageNumber}"]`);
    if (directPageLink) {
      console.log(`📍 تم العثور على رابط مباشر للصفحة ${nextPageNumber}`);
      success = await clickElement(directPageLink);

      if (success) {
        console.log(`✅ نجح التنقل باستخدام الرابط المباشر`);
        return true;
      }
    }

    // البحث عن أزرار التنقل بالنصوص
    const nextButtons = Array.from(document.querySelectorAll('a, button')).filter(el => {
      const text = el.textContent.trim();
      return text.includes('›') || text.includes('»') ||
             text.includes('التالي') || text.includes('Next') ||
             text === '>' || el.classList.contains('next');
    });

    for (const button of nextButtons) {
      console.log('📍 محاولة زر التالي:', button.textContent.trim());
      success = await clickElement(button);
      if (success) {
        console.log('✅ نجح التنقل باستخدام زر التالي البديل');
        return true;
      }
    }
  }

  if (!success) {
    console.error('❌ فشل في جميع محاولات التنقل');
    throw new Error(`فشل في التنقل إلى الصفحة ${nextPageNumber}`);
  }

  return success;
}

// دالة للنقر على عنصر مع معالجة الأحداث المختلفة
async function clickElement(element) {
  try {
    console.log('🖱️ محاولة النقر على العنصر:', {
      tagName: element.tagName,
      textContent: element.textContent.trim(),
      dataPage: element.getAttribute('data-page'),
      href: element.getAttribute('href'),
      disabled: element.disabled,
      className: element.className
    });

    // التحقق من أن العنصر مرئي وقابل للنقر
    if (!element || element.disabled || element.classList.contains('disabled')) {
      console.log('❌ العنصر غير قابل للنقر');
      return false;
    }

    // التحقق من أن العنصر مرئي
    const rect = element.getBoundingClientRect();
    if (rect.width === 0 || rect.height === 0) {
      console.log('❌ العنصر غير مرئي');
      return false;
    }

    // محاولة النقر بطرق متعددة
    let clickSuccess = false;

    // الطريقة 1: النقر العادي
    try {
      console.log('🔄 محاولة النقر العادي...');
      element.click();
      clickSuccess = true;
      console.log('✅ نجح النقر العادي');
    } catch (e) {
      console.warn('⚠️ فشل النقر العادي:', e);
    }

    // الطريقة 2: تنفيذ onclick إذا وجد
    if (element.onclick && !clickSuccess) {
      try {
        console.log('🔄 محاولة تنفيذ onclick...');
        element.onclick.call(element);
        clickSuccess = true;
        console.log('✅ نجح تنفيذ onclick');
      } catch (e) {
        console.warn('⚠️ فشل تنفيذ onclick:', e);
      }
    }

    // الطريقة 3: إرسال أحداث الماوس
    if (!clickSuccess) {
      try {
        console.log('🔄 محاولة إرسال أحداث الماوس...');

        // إرسال أحداث متتالية
        const events = ['mousedown', 'mouseup', 'click'];
        for (const eventType of events) {
          const mouseEvent = new MouseEvent(eventType, {
            bubbles: true,
            cancelable: true,
            view: window,
            button: 0
          });
          element.dispatchEvent(mouseEvent);
        }

        clickSuccess = true;
        console.log('✅ نجح إرسال أحداث الماوس');
      } catch (e) {
        console.warn('⚠️ فشل إرسال أحداث الماوس:', e);
      }
    }

    // الطريقة 4: محاولة التنقل المباشر للروابط
    if (!clickSuccess && element.tagName === 'A' && element.href) {
      try {
        console.log('🔄 محاولة التنقل المباشر...');

        // إذا كان الرابط يحتوي على JavaScript
        if (element.href.startsWith('javascript:')) {
          eval(element.href.substring(11));
        } else if (element.href.startsWith('#')) {
          // رابط محلي - لا نفعل شيء
          console.log('🔗 رابط محلي - تم تجاهله');
        } else {
          // رابط عادي
          window.location.href = element.href;
        }

        clickSuccess = true;
        console.log('✅ نجح التنقل المباشر');
      } catch (e) {
        console.warn('⚠️ فشل التنقل المباشر:', e);
      }
    }

    // انتظار قصير للتحقق من حدوث التغيير
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log(`${clickSuccess ? '✅' : '❌'} نتيجة النقر: ${clickSuccess ? 'نجح' : 'فشل'}`);
    return clickSuccess;

  } catch (error) {
    console.error('❌ خطأ في النقر على العنصر:', error);
    return false;
  }
}

// دالة لانتظار تحميل الصفحة الجديدة
async function waitForPageLoad(timeout = 5000) {
  console.log('⏳ انتظار تحميل الصفحة الجديدة...');

  return new Promise((resolve) => {
    let timeoutId;
    let checkInterval;
    let checkCount = 0;
    const maxChecks = 20; // 10 ثوان كحد أقصى

    // الحصول على محتوى الجدول الحالي للمقارنة
    const currentTable = findBestTable();
    const initialTableContent = currentTable ? currentTable.innerHTML : '';
    const initialPaginationState = getCurrentPaginationState();

    const cleanup = () => {
      if (timeoutId) clearTimeout(timeoutId);
      if (checkInterval) clearInterval(checkInterval);
    };

    // مهلة زمنية للانتظار
    timeoutId = setTimeout(() => {
      cleanup();
      console.log('⚠️ انتهت مهلة انتظار تحميل الصفحة - المتابعة...');
      resolve(true); // المتابعة بدلاً من الرفض
    }, timeout);

    // فحص دوري لتغيير المحتوى
    checkInterval = setInterval(() => {
      checkCount++;

      // التحقق من تغيير محتوى الجدول
      const newTable = findBestTable();
      const newTableContent = newTable ? newTable.innerHTML : '';
      const newPaginationState = getCurrentPaginationState();

      // التحقق من تغيير المحتوى أو حالة التنقل
      if (newTableContent !== initialTableContent ||
          newPaginationState !== initialPaginationState ||
          checkCount >= maxChecks) {

        console.log('✅ تم اكتشاف تغيير في الصفحة');
        cleanup();

        // انتظار إضافي قصير للتأكد من اكتمال التحميل
        setTimeout(() => {
          resolve(true);
        }, 500);
      }
    }, 500);
  });
}

// دالة للحصول على حالة التنقل الحالية
function getCurrentPaginationState() {
  const paginationElements = document.querySelectorAll('.dataTable-pagination, .pagination, .page-numbers');
  return Array.from(paginationElements).map(el => el.innerHTML).join('');
}

// دالة للكشط التلقائي لجميع الصفحات
async function autoScrapeAllPages(options = {}) {
  console.log('🚀 بدء الكشط التلقائي لجميع الصفحات...');

  try {
    // إعادة تعيين المتغيرات
    autoScrapeInProgress = true;
    autoScrapePaused = false;
    autoScrapeData = [];

    // اكتشاف عناصر التنقل
    currentPaginationInfo = detectPaginationElements();

    if (!currentPaginationInfo) {
      throw new Error('لم يتم العثور على عناصر التنقل في الصفحة');
    }

    console.log(`📊 تم اكتشاف ${currentPaginationInfo.totalPages} صفحة`);

    // كشط جميع الصفحات
    for (let page = currentPaginationInfo.currentPage; page <= currentPaginationInfo.totalPages; page++) {
      // التحقق من حالة الإيقاف
      if (!autoScrapeInProgress) {
        console.log('🛑 تم إيقاف الكشط التلقائي');
        break;
      }

      // التحقق من حالة الإيقاف المؤقت
      while (autoScrapePaused && autoScrapeInProgress) {
        console.log('⏸️ الكشط متوقف مؤقتاً...');
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      console.log(`📄 كشط الصفحة ${page} من ${currentPaginationInfo.totalPages}`);

      // تحديث الصفحة الحالية في المتغير العام
      currentPaginationInfo.currentPage = page;

      // كشط الصفحة الحالية
      const table = findBestTable(options.tableType);
      if (table) {
        const pageData = scrapeTableData(table);
        autoScrapeData.push(pageData);
        console.log(`✅ تم كشط ${pageData.length} صف من الصفحة ${page}`);
      } else {
        console.warn(`⚠️ لم يتم العثور على جدول في الصفحة ${page}`);
        // إضافة صفحة فارغة للحفاظ على ترقيم الصفحات
        autoScrapeData.push([]);
      }

      // إرسال تحديث التقدم
      try {
        chrome.runtime.sendMessage({
          action: "autoScrapeProgress",
          currentPage: page,
          totalPages: currentPaginationInfo.totalPages,
          scrapedPages: autoScrapeData.length
        });
      } catch (e) {
        console.warn('⚠️ فشل في إرسال تحديث التقدم:', e);
      }

      // التنقل إلى الصفحة التالية (إذا لم تكن الصفحة الأخيرة)
      if (page < currentPaginationInfo.totalPages) {
        try {
          console.log(`🔄 التنقل من الصفحة ${page} إلى الصفحة ${page + 1}...`);

          const navigationSuccess = await navigateToNextPage(currentPaginationInfo);
          if (!navigationSuccess) {
            throw new Error('فشل في التنقل');
          }

          console.log('⏳ انتظار تحميل الصفحة الجديدة...');
          await waitForPageLoad();

          // تحديث معلومات التنقل للصفحة الجديدة
          console.log('🔄 تحديث معلومات التنقل...');
          const updatedPaginationInfo = detectPaginationElements();
          if (updatedPaginationInfo) {
            currentPaginationInfo = updatedPaginationInfo;
            console.log(`✅ تم تحديث معلومات التنقل - الصفحة الحالية: ${updatedPaginationInfo.currentPage}`);
          } else {
            console.warn('⚠️ فشل في تحديث معلومات التنقل');
          }

        } catch (error) {
          console.error(`❌ خطأ في التنقل إلى الصفحة ${page + 1}:`, error);

          // محاولة إضافية للتنقل
          console.log('🔄 محاولة إضافية للتنقل...');
          try {
            // البحث عن رابط الصفحة التالية مباشرة
            const nextPageLink = document.querySelector(`[data-page="${page + 1}"]`);
            if (nextPageLink) {
              console.log('🔗 تم العثور على رابط الصفحة التالية مباشرة');
              await clickElement(nextPageLink);
              await waitForPageLoad();

              // تحديث معلومات التنقل
              const retryPaginationInfo = detectPaginationElements();
              if (retryPaginationInfo) {
                currentPaginationInfo = retryPaginationInfo;
                console.log('✅ نجحت المحاولة الإضافية');
                continue; // متابعة الحلقة
              }
            }
          } catch (retryError) {
            console.error('❌ فشلت المحاولة الإضافية:', retryError);
          }

          // إذا فشل كل شيء، توقف
          console.error('❌ فشل في التنقل نهائياً - إيقاف الكشط');
          break;
        }
      }

      // تأخير قصير بين الصفحات لتجنب الحمل الزائد
      await new Promise(resolve => setTimeout(resolve, 1500));
    }

    console.log(`🎉 تم الانتهاء من الكشط التلقائي! تم كشط ${autoScrapeData.length} صفحة`);

    return {
      success: true,
      data: autoScrapeData,
      totalPages: currentPaginationInfo.totalPages,
      scrapedPages: autoScrapeData.length
    };

  } catch (error) {
    console.error('❌ خطأ في الكشط التلقائي:', error);
    return {
      success: false,
      error: error.message,
      data: autoScrapeData // إرجاع البيانات المكشوطة حتى لو حدث خطأ
    };
  } finally {
    autoScrapeInProgress = false;
    autoScrapePaused = false;
  }
}
