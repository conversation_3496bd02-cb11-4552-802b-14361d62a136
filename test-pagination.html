<!DOCTYPE html>
<html dir="rtl">
<head>
  <meta charset="UTF-8">
  <title>اختبار الكشط التلقائي</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
      direction: rtl;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: right;
    }
    th {
      background-color: #f2f2f2;
    }
    .dataTable-pagination {
      margin-top: 20px;
      text-align: center;
    }
    .dataTable-pagination-list {
      list-style: none;
      padding: 0;
      display: inline-flex;
      gap: 5px;
    }
    .dataTable-pagination-list li {
      display: inline-block;
    }
    .dataTable-pagination-list a {
      display: block;
      padding: 8px 12px;
      text-decoration: none;
      border: 1px solid #ddd;
      color: #333;
    }
    .dataTable-pagination-list .active a {
      background-color: #007cba;
      color: white;
      border-color: #007cba;
    }
    .dataTable-pagination-list a:hover {
      background-color: #f5f5f5;
    }
  </style>
</head>
<body>
  <h1>اختبار الكشط التلقائي للجداول</h1>
  
  <table id="testTable">
    <thead>
      <tr>
        <th>الرقم</th>
        <th>الاسم</th>
        <th>البريد الإلكتروني</th>
        <th>التاريخ</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>1</td>
        <td>أحمد محمد</td>
        <td><EMAIL></td>
        <td>2024-01-01</td>
      </tr>
      <tr>
        <td>2</td>
        <td>فاطمة علي</td>
        <td><EMAIL></td>
        <td>2024-01-02</td>
      </tr>
      <tr>
        <td>3</td>
        <td>محمد حسن</td>
        <td><EMAIL></td>
        <td>2024-01-03</td>
      </tr>
    </tbody>
  </table>

  <!-- عناصر التنقل للاختبار -->
  <nav class="dataTable-pagination">
    <ul class="dataTable-pagination-list">
      <li class="pager"><a href="#" data-page="1">‹</a></li>
      <li class=""><a href="#" data-page="1">1</a></li>
      <li class=""><a href="#" data-page="2">2</a></li>
      <li class=""><a href="#" data-page="3">3</a></li>
      <li class=""><a href="#" data-page="4">4</a></li>
      <li class=""><a href="#" data-page="5">5</a></li>
      <li class=""><a href="#" data-page="6">6</a></li>
      <li class="active"><a href="#" data-page="7">7</a></li>
      <li class=""><a href="#" data-page="8">8</a></li>
      <li class=""><a href="#" data-page="9">9</a></li>
      <li class=""><a href="#" data-page="10">10</a></li>
      <li class="pager"><a href="#" data-page="8">›</a></li>
    </ul>
  </nav>

  <script>
    // محاكاة تغيير الصفحات
    document.querySelectorAll('.dataTable-pagination a[data-page]').forEach(link => {
      link.addEventListener('click', function(e) {
        e.preventDefault();
        const page = this.getAttribute('data-page');
        console.log('تم النقر على الصفحة:', page);
        
        // إزالة الحالة النشطة من جميع العناصر
        document.querySelectorAll('.dataTable-pagination-list li').forEach(li => {
          li.classList.remove('active');
        });
        
        // إضافة الحالة النشطة للعنصر المحدد
        this.closest('li').classList.add('active');
        
        // محاكاة تغيير محتوى الجدول
        updateTableContent(page);
      });
    });
    
    function updateTableContent(page) {
      const tbody = document.querySelector('#testTable tbody');
      tbody.innerHTML = '';
      
      for (let i = 1; i <= 3; i++) {
        const row = tbody.insertRow();
        row.insertCell(0).textContent = ((page - 1) * 3) + i;
        row.insertCell(1).textContent = `مستخدم ${((page - 1) * 3) + i}`;
        row.insertCell(2).textContent = `user${((page - 1) * 3) + i}@example.com`;
        row.insertCell(3).textContent = `2024-01-${String(((page - 1) * 3) + i).padStart(2, '0')}`;
      }
    }
  </script>
</body>
</html>
