document.addEventListener('DOMContentLoaded', function() {
  const scrapeButton = document.getElementById('scrapeButton');
  const selectTableButton = document.getElementById('selectTableButton');
  const statusDiv = document.getElementById('status');
  const autoSelector = document.querySelector('input[name="tableSelector"][value="auto"]');
  const manualSelector = document.querySelector('input[name="tableSelector"][value="manual"]');
  const singleMode = document.querySelector('input[name="scrapeMode"][value="single"]');
  const multiMode = document.querySelector('input[name="scrapeMode"][value="multi"]');
  const tableTypeAuto = document.querySelector('input[name="tableType"][value="auto"]');
  const tableTypeTraditional = document.querySelector('input[name="tableType"][value="traditional"]');
  const tableTypeResponsive = document.querySelector('input[name="tableType"][value="responsive"]');

  // التحقق من وجود عملية كشط متعددة الصفحات جارية
  chrome.storage.local.get(['scrapedData', 'isMultiPageScraping'], function(result) {
    if (result.isMultiPageScraping && result.scrapedData && result.scrapedData.length > 0) {
      // عرض رسالة للمستخدم
      statusDiv.textContent = 'هناك عملية كشط متعددة الصفحات جارية. يمكنك متابعة الكشط أو إنهاء العملية.';
      statusDiv.className = 'status success';
      statusDiv.style.display = 'block';

      // تغيير نص زر الكشط
      scrapeButton.textContent = 'متابعة الكشط';

      // تحديد وضع الكشط المتعدد
      multiMode.checked = true;
    }
  });

  // تحديث حالة الأزرار عند تغيير طريقة التحديد
  function updateButtonsState() {
    if (manualSelector.checked) {
      selectTableButton.style.display = 'block';
    } else {
      selectTableButton.style.display = 'none';
    }
  }

  // إضافة مستمعي الأحداث لأزرار الاختيار
  autoSelector.addEventListener('change', updateButtonsState);
  manualSelector.addEventListener('change', updateButtonsState);

  // زر تحديد الجدول
  selectTableButton.addEventListener('click', async function() {
    try {
      // الحصول على علامة التبويب النشطة
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      // تغيير حالة الزر
      selectTableButton.disabled = true;
      selectTableButton.textContent = 'انقر على الجدول في الصفحة...';
      statusDiv.textContent = 'انقر على الجدول الذي تريد كشطه في الصفحة';
      statusDiv.className = 'status success';
      statusDiv.style.display = 'block';

      // إرسال رسالة لتفعيل وضع تحديد الجدول
      chrome.tabs.sendMessage(tab.id, { action: "enableTableSelection" }, function(response) {
        if (response && response.success) {
          // إغلاق النافذة المنبثقة لتمكين المستخدم من النقر على الجدول
          window.close();
        } else {
          // عرض رسالة خطأ
          statusDiv.textContent = 'حدث خطأ أثناء تفعيل وضع التحديد: ' + (response ? response.error : 'لا يوجد استجابة');
          statusDiv.className = 'status error';
          statusDiv.style.display = 'block';

          // إعادة تفعيل الزر
          selectTableButton.disabled = false;
          selectTableButton.textContent = 'تحديد الجدول';
        }
      });
    } catch (error) {
      // عرض رسالة خطأ
      statusDiv.textContent = 'حدث خطأ: ' + error.message;
      statusDiv.className = 'status error';
      statusDiv.style.display = 'block';

      // إعادة تفعيل الزر
      selectTableButton.disabled = false;
      selectTableButton.textContent = 'تحديد الجدول';
    }
  });

  // زر كشط الجدول
  scrapeButton.addEventListener('click', async function() {
    // تغيير حالة الزر
    scrapeButton.disabled = true;
    scrapeButton.textContent = 'جاري الكشط...';

    try {
      // الحصول على علامة التبويب النشطة
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      // تحديد الخيارات
      const options = {
        selectionMode: autoSelector.checked ? 'auto' : 'manual',
        exportFormat: 'csv',
        multiPage: multiMode.checked,
        tableType: getSelectedTableType()
      };

      // دالة للحصول على نوع الجدول المحدد
      function getSelectedTableType() {
        if (tableTypeTraditional.checked) return 'traditional';
        if (tableTypeResponsive.checked) return 'responsive';
        return 'auto'; // القيمة الافتراضية
      }

      // التحقق من وجود عملية كشط متعددة الصفحات جارية
      chrome.storage.local.get(['scrapedData', 'isMultiPageScraping'], function(result) {
        // إذا كانت هناك عملية كشط متعددة الصفحات جارية، أضف البيانات الموجودة إلى الخيارات
        if (result.isMultiPageScraping && result.scrapedData) {
          options.existingData = result.scrapedData;
          options.continueScraping = true;
        }

        // تنفيذ سكريبت في الصفحة الحالية
        chrome.tabs.sendMessage(tab.id, {
          action: "scrapeTable",
          options: options
        }, function(response) {
          if (response && response.success) {
            if (options.multiPage) {
              // إذا كان وضع الكشط متعدد الصفحات
              // تخزين البيانات وفتح صفحة النتائج
              chrome.storage.local.set({
                scrapedData: response.data,
                isMultiPageScraping: true
              }, function() {
                // التحقق من وجود صفحة نتائج مفتوحة بالفعل
                chrome.storage.local.get(['resultsTabId'], function(result) {
                  if (result.resultsTabId) {
                    // التحقق من وجود علامة التبويب
                    chrome.tabs.get(result.resultsTabId, function(tab) {
                      if (tab && !chrome.runtime.lastError) {
                        // إذا كانت علامة التبويب موجودة، قم بتحديثها
                        chrome.tabs.update(result.resultsTabId, { active: true }, function() {
                          // إرسال رسالة لتحديث البيانات
                          chrome.tabs.sendMessage(result.resultsTabId, { action: "updateData" });
                          // إغلاق النافذة المنبثقة
                          window.close();
                        });
                      } else {
                        // إذا لم تكن علامة التبويب موجودة، قم بإنشاء واحدة جديدة
                        createNewResultsTab();
                      }
                    });
                  } else {
                    // إذا لم تكن هناك علامة تبويب مخزنة، قم بإنشاء واحدة جديدة
                    createNewResultsTab();
                  }
                });
              });

              // دالة لإنشاء علامة تبويب نتائج جديدة
              function createNewResultsTab() {
                chrome.tabs.create({ url: 'results.html' }, function(tab) {
                  // تخزين معرف علامة التبويب
                  chrome.storage.local.set({ resultsTabId: tab.id });
                  // إغلاق النافذة المنبثقة
                  window.close();
                });
              }
            } else {
              // عرض رسالة نجاح
              statusDiv.textContent = 'تم كشط الجدول بنجاح وجاري تحميل الملف!';
              statusDiv.className = 'status success';
              statusDiv.style.display = 'block';
            }
          } else {
            // عرض رسالة خطأ
            statusDiv.textContent = 'حدث خطأ أثناء كشط الجدول: ' + (response ? response.error : 'لا يوجد استجابة');
            statusDiv.className = 'status error';
            statusDiv.style.display = 'block';
          }

          // إعادة تفعيل الزر
          scrapeButton.disabled = false;
          scrapeButton.textContent = 'كشط الجدول';
        });
      });
    } catch (error) {
      // عرض رسالة خطأ
      statusDiv.textContent = 'حدث خطأ: ' + error.message;
      statusDiv.className = 'status error';
      statusDiv.style.display = 'block';

      // إعادة تفعيل الزر
      scrapeButton.disabled = false;
      scrapeButton.textContent = 'كشط الجدول';
    }
  });

  // تهيئة حالة الأزرار عند بدء التشغيل
  updateButtonsState();
});
