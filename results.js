// متغيرات عامة
let allData = [];
let currentPageNum = 0;

// عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
  // تسجيل معرف علامة التبويب الحالية
  chrome.tabs.getCurrent(function(tab) {
    if (tab) {
      chrome.storage.local.set({ resultsTabId: tab.id });
    }
  });

  // الحصول على البيانات المخزنة
  loadData();

  // إضافة مستمعي الأحداث للأزرار
  document.getElementById('continue-button').addEventListener('click', continueScrapingHandler);
  document.getElementById('download-button').addEventListener('click', downloadDataHandler);
});

// دالة لتحميل البيانات
function loadData() {
  chrome.storage.local.get(['scrapedData'], function(result) {
    if (result.scrapedData && result.scrapedData.length > 0) {
      // تخزين البيانات في المتغير العام
      allData = result.scrapedData;

      // عرض البيانات
      displayData();

      // إخفاء رسالة "لا توجد بيانات"
      document.getElementById('no-data-message').style.display = 'none';
    }
  });
}

// استماع للرسائل من popup.js
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  if (request.action === "updateData") {
    // تحديث البيانات عند استلام رسالة التحديث
    loadData();
    return true;
  }
});

// دالة لعرض البيانات المكشوطة
function displayData() {
  const container = document.getElementById('results-container');

  // مسح المحتوى الحالي (باستثناء رسالة "لا توجد بيانات")
  const noDataMessage = document.getElementById('no-data-message');
  container.innerHTML = '';
  container.appendChild(noDataMessage);
  noDataMessage.style.display = 'none';

  // عرض بيانات كل صفحة
  allData.forEach((pageData, pageIndex) => {
    // إنشاء عنصر للصفحة
    const pageElement = document.createElement('div');
    pageElement.className = 'page-data';

    // إضافة عنوان الصفحة
    const pageTitle = document.createElement('div');
    pageTitle.className = 'page-title';
    pageTitle.textContent = `الصفحة ${pageIndex + 1}`;
    pageElement.appendChild(pageTitle);

    // إنشاء جدول لعرض البيانات
    if (pageData.length > 0) {
      const table = document.createElement('table');

      // إنشاء صف الرؤوس (إذا كان هناك بيانات)
      if (pageData[0].length > 0) {
        const headerRow = document.createElement('tr');

        // إضافة خلايا الرؤوس
        for (let i = 0; i < pageData[0].length; i++) {
          const headerCell = document.createElement('th');
          headerCell.textContent = pageData[0][i] || `عمود ${i + 1}`;
          headerRow.appendChild(headerCell);
        }

        // إضافة عمود الملاحظات
        const notesHeaderCell = document.createElement('th');
        notesHeaderCell.textContent = 'الملاحظات';
        headerRow.appendChild(notesHeaderCell);

        // إضافة صف الرؤوس إلى الجدول
        const thead = document.createElement('thead');
        thead.appendChild(headerRow);
        table.appendChild(thead);

        // إنشاء جسم الجدول
        const tbody = document.createElement('tbody');

        // إضافة صفوف البيانات (بدءًا من الصف الثاني)
        for (let i = 1; i < pageData.length; i++) {
          const dataRow = document.createElement('tr');

          // إضافة خلايا البيانات
          for (let j = 0; j < pageData[i].length; j++) {
            const dataCell = document.createElement('td');
            dataCell.textContent = pageData[i][j] || '';
            dataRow.appendChild(dataCell);
          }

          // إضافة خلية الملاحظات
          const notesCell = document.createElement('td');
          notesCell.className = 'notes-cell';

          // إنشاء حقل إدخال للملاحظات
          const notesInput = document.createElement('textarea');
          notesInput.className = 'notes-input';
          notesInput.placeholder = 'أضف ملاحظاتك هنا...';

          // التحقق من وجود ملاحظات محفوظة مسبقًا
          if (pageData[i].notes) {
            notesInput.value = pageData[i].notes;
          }

          // إضافة مستمع الحدث لحفظ الملاحظات عند التغيير
          notesInput.addEventListener('input', function() {
            // حفظ الملاحظات في مصفوفة البيانات
            pageData[i].notes = this.value;

            // تحديث البيانات المخزنة
            chrome.storage.local.set({ scrapedData: allData });
          });

          notesCell.appendChild(notesInput);
          dataRow.appendChild(notesCell);

          // إضافة صف البيانات إلى جسم الجدول
          tbody.appendChild(dataRow);
        }

        // إضافة جسم الجدول إلى الجدول
        table.appendChild(tbody);
      }

      // إضافة الجدول إلى عنصر الصفحة
      pageElement.appendChild(table);
    } else {
      // إذا لم تكن هناك بيانات للصفحة
      const noDataMsg = document.createElement('p');
      noDataMsg.textContent = 'لا توجد بيانات لهذه الصفحة';
      pageElement.appendChild(noDataMsg);
    }

    // إضافة عنصر الصفحة إلى الحاوية
    container.appendChild(pageElement);
  });

  // تحديث رقم الصفحة الحالية
  currentPageNum = allData.length;
}

// دالة لمتابعة الكشط
function continueScrapingHandler() {
  // إرسال رسالة إلى الـ background script للعودة إلى صفحة الكشط
  chrome.runtime.sendMessage({
    action: "continueScraping",
    currentPageNum: currentPageNum
  });

  // إغلاق نافذة النتائج
  window.close();
}

// دالة لتحميل البيانات
function downloadDataHandler() {
  if (allData.length === 0) {
    alert('لا توجد بيانات للتحميل');
    return;
  }

  // الحصول على التاريخ الحالي لاستخدامه في اسم الملف الافتراضي
  const date = new Date();
  const dateString = date.getFullYear() + '-' +
                    (date.getMonth() + 1).toString().padStart(2, '0') + '-' +
                    date.getDate().toString().padStart(2, '0') + '_' +
                    date.getHours().toString().padStart(2, '0') + '-' +
                    date.getMinutes().toString().padStart(2, '0');

  // إنشاء مربع حوار لتحديد اسم الملف
  showFileNameDialog(`جدول_منصة_اعتماد_${dateString}`, function(fileName) {
    if (!fileName) {
      // إذا قام المستخدم بإلغاء العملية
      return;
    }

    // تحويل البيانات إلى تنسيق CSV
    let csvContent = '';

    // دمج بيانات جميع الصفحات
    let mergedData = [];

    // استخدام رؤوس الصفحة الأولى مع إضافة عمود الملاحظات
    if (allData.length > 0 && allData[0].length > 0) {
      // نسخ رؤوس الصفحة الأولى
      const headers = [...allData[0][0]];
      // إضافة عمود الملاحظات إلى الرؤوس
      headers.push('الملاحظات');
      mergedData.push(headers);

      // إضافة بيانات جميع الصفحات (بدون الرؤوس)
      allData.forEach((pageData) => {
        for (let i = 1; i < pageData.length; i++) {
          // نسخ بيانات الصف
          const rowData = [...pageData[i]];
          // إضافة الملاحظات إلى الصف
          rowData.push(pageData[i].notes || '');
          mergedData.push(rowData);
        }
      });
    }

    // تحويل البيانات المدمجة إلى CSV
    mergedData.forEach((row) => {
      const csvRow = row.map((cell) => {
        // إذا كانت الخلية تحتوي على فاصلة أو اقتباس أو سطر جديد، ضعها بين علامات اقتباس
        if (cell && (cell.includes(',') || cell.includes('"') || cell.includes('\n'))) {
          // استبدال علامات الاقتباس بعلامتي اقتباس متتاليتين
          return '"' + cell.replace(/"/g, '""') + '"';
        }
        return cell || '';
      }).join(',');

      csvContent += csvRow + '\n';
    });

    // عرض رسالة للمستخدم قبل بدء التحميل
    const downloadButton = document.getElementById('download-button');
    downloadButton.textContent = 'جاري التحميل...';
    downloadButton.disabled = true;

    // التأكد من وجود امتداد الملف
    if (!fileName.toLowerCase().endsWith('.csv')) {
      fileName += '.csv';
    }

    try {
      // إنشاء Blob من البيانات
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

      // إنشاء رابط التحميل
      const url = URL.createObjectURL(blob);

      // إنشاء عنصر الرابط وتنفيذ النقر عليه
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      a.style.display = 'none';
      document.body.appendChild(a);
      a.click();

      // إزالة الرابط بعد التحميل
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        // مسح البيانات المخزنة بعد التحميل
        chrome.storage.local.remove(['scrapedData', 'isMultiPageScraping', 'resultsTabId'], function() {
          console.log('تم مسح البيانات المخزنة');
        });

        // تحديث حالة الزر
        downloadButton.textContent = 'تم التحميل بنجاح';

        // إعادة تشغيل الإضافة من البداية
        setTimeout(() => {
          // مسح محتوى الصفحة وإظهار رسالة "لا توجد بيانات"
          allData = [];
          const container = document.getElementById('results-container');
          const noDataMessage = document.getElementById('no-data-message');
          container.innerHTML = '';
          container.appendChild(noDataMessage);
          noDataMessage.style.display = 'block';

          // تحديث حالة الزر
          downloadButton.textContent = 'إنهاء وتحميل';
          downloadButton.disabled = false;

          // إظهار رسالة للمستخدم
          const successMessage = document.createElement('div');
          successMessage.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #4CAF50;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            z-index: 1000;
            font-size: 16px;
            direction: rtl;
          `;
          successMessage.textContent = 'تم التحميل بنجاح! يمكنك الآن بدء عملية كشط جديدة.';
          document.body.appendChild(successMessage);

          // إزالة الرسالة بعد 3 ثوان
          setTimeout(() => {
            if (document.body.contains(successMessage)) {
              document.body.removeChild(successMessage);
            }
          }, 3000);
        }, 2000);
      }, 100);
    } catch (error) {
      console.error('خطأ في التحميل:', error);

      // عرض رسالة خطأ للمستخدم
      alert('حدث خطأ أثناء التحميل. يرجى المحاولة مرة أخرى.');

      // إعادة تفعيل الزر
      downloadButton.textContent = 'إنهاء وتحميل';
      downloadButton.disabled = false;
    }
  });
}

// دالة لعرض مربع حوار لتحديد اسم الملف
function showFileNameDialog(defaultFileName, callback) {
  // إنشاء عناصر مربع الحوار
  const dialogOverlay = document.createElement('div');
  dialogOverlay.className = 'dialog-overlay';
  dialogOverlay.style.position = 'fixed';
  dialogOverlay.style.top = '0';
  dialogOverlay.style.left = '0';
  dialogOverlay.style.width = '100%';
  dialogOverlay.style.height = '100%';
  dialogOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
  dialogOverlay.style.zIndex = '1000';
  dialogOverlay.style.display = 'flex';
  dialogOverlay.style.justifyContent = 'center';
  dialogOverlay.style.alignItems = 'center';

  const dialogBox = document.createElement('div');
  dialogBox.className = 'dialog-box';
  dialogBox.style.backgroundColor = 'white';
  dialogBox.style.padding = '20px';
  dialogBox.style.borderRadius = '8px';
  dialogBox.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
  dialogBox.style.width = '400px';
  dialogBox.style.maxWidth = '90%';
  dialogBox.style.textAlign = 'right';
  dialogBox.style.direction = 'rtl';

  const dialogTitle = document.createElement('h3');
  dialogTitle.textContent = 'تحديد اسم الملف';
  dialogTitle.style.marginTop = '0';
  dialogTitle.style.marginBottom = '15px';

  const fileNameInput = document.createElement('input');
  fileNameInput.type = 'text';
  fileNameInput.value = defaultFileName;
  fileNameInput.style.width = '100%';
  fileNameInput.style.padding = '8px';
  fileNameInput.style.marginBottom = '15px';
  fileNameInput.style.boxSizing = 'border-box';
  fileNameInput.style.direction = 'rtl';

  const buttonContainer = document.createElement('div');
  buttonContainer.style.display = 'flex';
  buttonContainer.style.justifyContent = 'flex-start';
  buttonContainer.style.gap = '10px';

  const cancelButton = document.createElement('button');
  cancelButton.textContent = 'إلغاء';
  cancelButton.style.padding = '8px 15px';
  cancelButton.style.backgroundColor = '#f44336';
  cancelButton.style.color = 'white';
  cancelButton.style.border = 'none';
  cancelButton.style.borderRadius = '4px';
  cancelButton.style.cursor = 'pointer';

  const confirmButton = document.createElement('button');
  confirmButton.textContent = 'تحميل';
  confirmButton.style.padding = '8px 15px';
  confirmButton.style.backgroundColor = '#4CAF50';
  confirmButton.style.color = 'white';
  confirmButton.style.border = 'none';
  confirmButton.style.borderRadius = '4px';
  confirmButton.style.cursor = 'pointer';

  // إضافة مستمعي الأحداث
  cancelButton.addEventListener('click', function() {
    document.body.removeChild(dialogOverlay);
    callback(null); // استدعاء الدالة مع قيمة null للإشارة إلى الإلغاء
  });

  confirmButton.addEventListener('click', function() {
    const fileName = fileNameInput.value.trim() || defaultFileName;
    document.body.removeChild(dialogOverlay);
    callback(fileName); // استدعاء الدالة مع اسم الملف
  });

  // معالجة ضغط مفتاح Enter
  fileNameInput.addEventListener('keyup', function(event) {
    if (event.key === 'Enter') {
      confirmButton.click();
    }
  });

  // تجميع مربع الحوار
  buttonContainer.appendChild(confirmButton);
  buttonContainer.appendChild(cancelButton);

  dialogBox.appendChild(dialogTitle);
  dialogBox.appendChild(fileNameInput);
  dialogBox.appendChild(buttonContainer);

  dialogOverlay.appendChild(dialogBox);
  document.body.appendChild(dialogOverlay);

  // تركيز حقل الإدخال
  fileNameInput.focus();
  fileNameInput.select();
}
