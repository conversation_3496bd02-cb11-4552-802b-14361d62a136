<!DOCTYPE html>
<html dir="rtl">
<head>
  <meta charset="UTF-8">
  <title>كاشط جداول منصة اعتماد</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      width: 350px;
      padding: 10px;
      text-align: right;
    }
    button {
      width: 100%;
      padding: 10px;
      margin-top: 10px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }
    button:hover {
      background-color: #45a049;
    }
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    .status {
      margin-top: 10px;
      padding: 5px;
      border-radius: 4px;
      display: none;
    }
    .success {
      background-color: #d4edda;
      color: #155724;
    }
    .error {
      background-color: #f8d7da;
      color: #721c24;
    }
    .options {
      margin-top: 15px;
      border: 1px solid #ddd;
      padding: 10px;
      border-radius: 4px;
    }
    .option-title {
      font-weight: bold;
      margin-bottom: 5px;
    }
    label {
      display: block;
      margin: 5px 0;
    }
  </style>
</head>
<body>
  <h2>كاشط جداول منصة اعتماد</h2>

  <div class="options">
    <div class="option-title">طريقة تحديد الجدول:</div>
    <label>
      <input type="radio" name="tableSelector" value="auto" checked> تلقائي (البحث عن الجدول في الصفحة)
    </label>
    <label>
      <input type="radio" name="tableSelector" value="manual"> يدوي (تحديد الجدول بالنقر عليه)
    </label>
  </div>

  <div class="options">
    <div class="option-title">وضع الكشط:</div>
    <label>
      <input type="radio" name="scrapeMode" value="single" checked> صفحة واحدة
    </label>
    <label>
      <input type="radio" name="scrapeMode" value="multi"> متعدد الصفحات
    </label>
  </div>

  <div class="options">
    <div class="option-title">نوع الجدول المطلوب:</div>
    <label>
      <input type="radio" name="tableType" value="auto" checked> تلقائي (كشف جميع أنواع الجداول)
    </label>
    <label>
      <input type="radio" name="tableType" value="traditional"> جداول HTML تقليدية فقط
    </label>
    <label>
      <input type="radio" name="tableType" value="responsive"> جداول متجاوبة مع data-label فقط
    </label>
  </div>

  <div class="options">
    <div class="option-title">تنسيق التصدير:</div>
    <p>CSV (.csv) - يمكن فتحه بواسطة Excel</p>
  </div>

  <button id="selectTableButton" style="display: none;">تحديد الجدول</button>
  <button id="scrapeButton">كشط الجدول</button>
  <button id="openResultsButton">فتح صفحة النتائج</button>
  <div id="status" class="status"></div>

  <script src="popup.js"></script>
</body>
</html>
