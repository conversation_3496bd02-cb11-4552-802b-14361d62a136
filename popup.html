<!DOCTYPE html>
<html dir="rtl">
<head>
  <meta charset="UTF-8">
  <title>كاشط جداول منصة اعتماد</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      width: 350px;
      padding: 10px;
      text-align: right;
    }
    button {
      width: 100%;
      padding: 10px;
      margin-top: 10px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }
    button:hover {
      background-color: #45a049;
    }
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    .status {
      margin-top: 10px;
      padding: 5px;
      border-radius: 4px;
      display: none;
    }
    .success {
      background-color: #d4edda;
      color: #155724;
    }
    .error {
      background-color: #f8d7da;
      color: #721c24;
    }
    .options {
      margin-top: 15px;
      border: 1px solid #ddd;
      padding: 10px;
      border-radius: 4px;
    }
    .option-title {
      font-weight: bold;
      margin-bottom: 5px;
    }
    label {
      display: block;
      margin: 5px 0;
    }
    .auto-scrape-section {
      margin-top: 15px;
      border: 2px solid #2196F3;
      padding: 15px;
      border-radius: 6px;
      background-color: #f8f9ff;
    }
    .auto-scrape-title {
      font-weight: bold;
      margin-bottom: 10px;
      color: #2196F3;
      text-align: center;
    }
    #autoScrapeAllButton {
      background-color: #2196F3;
      position: relative;
      overflow: hidden;
    }
    #autoScrapeAllButton:hover {
      background-color: #0b7dda;
    }
    #autoScrapeAllButton:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    .progress-container {
      margin-top: 10px;
      display: none;
    }
    .progress-bar {
      width: 100%;
      height: 20px;
      background-color: #e0e0e0;
      border-radius: 10px;
      overflow: hidden;
      margin-bottom: 8px;
    }
    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #4CAF50, #2196F3);
      width: 0%;
      transition: width 0.3s ease;
      border-radius: 10px;
    }
    .progress-text {
      font-size: 14px;
      color: #333;
      text-align: center;
      display: block;
    }
    .auto-scrape-controls {
      margin-top: 10px;
      display: none;
      text-align: center;
    }
    .control-button {
      padding: 5px 10px;
      margin: 0 5px;
      font-size: 14px;
      border-radius: 3px;
    }
    .pause-button {
      background-color: #ff9800;
    }
    .pause-button:hover {
      background-color: #e68a00;
    }
    .stop-button {
      background-color: #f44336;
    }
    .stop-button:hover {
      background-color: #da190b;
    }
  </style>
</head>
<body>
  <h2>كاشط جداول منصة اعتماد</h2>

  <div class="options">
    <div class="option-title">طريقة تحديد الجدول:</div>
    <label>
      <input type="radio" name="tableSelector" value="auto" checked> تلقائي (البحث عن الجدول في الصفحة)
    </label>
    <label>
      <input type="radio" name="tableSelector" value="manual"> يدوي (تحديد الجدول بالنقر عليه)
    </label>
  </div>

  <div class="options">
    <div class="option-title">وضع الكشط:</div>
    <label>
      <input type="radio" name="scrapeMode" value="single" checked> صفحة واحدة
    </label>
    <label>
      <input type="radio" name="scrapeMode" value="multi"> متعدد الصفحات
    </label>
  </div>

  <div class="options">
    <div class="option-title">نوع الجدول المطلوب:</div>
    <label>
      <input type="radio" name="tableType" value="auto" checked> تلقائي (كشف جميع أنواع الجداول)
    </label>
    <label>
      <input type="radio" name="tableType" value="traditional"> جداول HTML تقليدية فقط
    </label>
    <label>
      <input type="radio" name="tableType" value="responsive"> جداول متجاوبة مع data-label فقط
    </label>
  </div>

  <div class="options">
    <div class="option-title">تنسيق التصدير:</div>
    <p>CSV (.csv) - يمكن فتحه بواسطة Excel</p>
  </div>

  <!-- قسم الكشط التلقائي الجديد -->
  <div class="auto-scrape-section">
    <div class="auto-scrape-title">🤖 الكشط التلقائي المتقدم</div>
    <button id="autoScrapeAllButton">كشط تلقائي لجميع الصفحات</button>

    <div id="autoScrapeProgress" class="progress-container">
      <div class="progress-bar">
        <div class="progress-fill"></div>
      </div>
      <span class="progress-text">جاري البحث عن الصفحات...</span>
    </div>

    <div id="autoScrapeControls" class="auto-scrape-controls">
      <button id="pauseAutoScrape" class="control-button pause-button">إيقاف مؤقت</button>
      <button id="stopAutoScrape" class="control-button stop-button">إيقاف</button>
    </div>
  </div>

  <button id="selectTableButton" style="display: none;">تحديد الجدول</button>
  <button id="scrapeButton">كشط الجدول</button>
  <button id="openResultsButton">فتح صفحة النتائج</button>
  <div id="status" class="status"></div>

  <script src="popup.js"></script>
</body>
</html>
